/* 🎨 MODERN MINIMALIST OTP AUTHENTICATION 2025 🎨 */

/* ===== DESIGN TOKENS ===== */
:root {
  /* Core Colors */
  --color-primary: #4361ee;
  --color-primary-light: #4895ef;
  --color-primary-dark: #3f37c9;

  --color-secondary: #4cc9f0;
  --color-secondary-light: #72efdd;
  --color-secondary-dark: #4361ee;

  --color-success: #4ade80;
  --color-warning: #fbbf24;
  --color-danger: #f87171;

  /* Neutrals */
  --color-background: #f8fafc;
  --color-surface: #ffffff;
  --color-text: #1e293b;
  --color-text-light: #64748b;
  --color-border: #e2e8f0;

  /* Semantic Colors */
  --color-focus: rgba(67, 97, 238, 0.5);
  --color-overlay: rgba(15, 23, 42, 0.1);

  /* Modern Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    var(--color-primary) 0%,
    var(--color-secondary) 100%
  );
  --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

  /* Elevation */
  --elevation-1: 0 2px 4px rgba(0, 0, 0, 0.04);
  --elevation-2: 0 4px 6px rgba(0, 0, 0, 0.06);
  --elevation-3: 0 8px 16px rgba(0, 0, 0, 0.08);
  --elevation-4: 0 12px 24px rgba(0, 0, 0, 0.12);

  /* Typography */
  --font-primary: "Albert Sans", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-display: "Cabinet Grotesk", -apple-system, BlinkMacSystemFont,
    sans-serif;

  /* Layout */
  --space-unit: 0.25rem;
  --border-radius-sm: 0.5rem;
  --border-radius-md: 0.75rem;
  --border-radius-lg: 1rem;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== GLOBAL STYLES ===== */
body {
  font-family: var(--font-primary);
  background-color: var(--color-background);
  color: var(--color-text);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Subtle Background Pattern */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 100% 0%,
      rgba(67, 97, 238, 0.05) 0%,
      transparent 25%
    ),
    radial-gradient(
      circle at 0% 100%,
      rgba(76, 201, 240, 0.05) 0%,
      transparent 25%
    );
  pointer-events: none;
  z-index: -1;
}

/* ===== NAVIGATION ===== */
.navbar {
  background: var(--color-surface) !important;
  box-shadow: var(--elevation-1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  padding: 1rem 0;
}

.navbar-brand {
  font-family: var(--font-display);
  color: var(--color-text);
  font-weight: 700;
  font-size: 1.5rem;
}

.navbar-brand i {
  color: var(--color-primary);
  margin-right: 0.5rem;
}

.nav-link {
  color: var(--color-text-light) !important;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: var(--transition-base);
  position: relative;
}

.nav-link:hover {
  color: var(--color-primary) !important;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 1rem;
  right: 1rem;
  height: 2px;
  background: var(--gradient-primary);
  opacity: 0;
  transform: scaleX(0.5);
  transition: var(--transition-base);
}

.nav-link:hover::after {
  opacity: 1;
  transform: scaleX(1);
}

/* ===== MODERN CARDS ===== */
.card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--elevation-1);
  transition: var(--transition-base);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--elevation-3);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid var(--color-border);
  padding: 1.5rem;
}

.card-header.bg-primary {
  background: var(--color-primary) !important;
}

.card-body {
  padding: 1.5rem;
}

.card-title {
  font-family: var(--font-display);
  color: var(--color-text);
  font-weight: 700;
  margin-bottom: 1rem;
}

/* ===== ELEGANT FORMS ===== */
.form-control {
  background: var(--color-surface);
  border: 1.5px solid var(--color-border);
  border-radius: var(--border-radius-md);
  color: var(--color-text);
  padding: 0.75rem 1rem;
  transition: var(--transition-base);
  font-size: 1rem;
}

.form-control:hover {
  border-color: var(--color-text-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px var(--color-focus);
  transform: scale(1.01);
}

.form-label {
  color: var(--color-text);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* ===== REFINED BUTTONS ===== */
.btn {
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-md);
  transition: var(--transition-base);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  border: none;
  color: white;
  box-shadow: var(--elevation-1);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--elevation-2);
  filter: brightness(1.1);
}

.btn-outline-primary {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.btn-outline-primary:hover {
  background: var(--gradient-primary);
  border-color: transparent;
  color: white;
}

/* ===== MODERN OTP INPUTS ===== */
.otp-container {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
}

.otp-digit {
  width: 56px;
  height: 64px;
  background: var(--color-surface);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-md);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text);
  transition: var(--transition-base);
  text-align: center;
}

.otp-digit:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px var(--color-focus);
  transform: scale(1.05);
}

/* ===== TABLE REFINEMENTS ===== */
.table {
  color: var(--color-text);
}

.table th {
  font-weight: 600;
  background: var(--color-background);
  border-bottom: 2px solid var(--color-border);
}

.table td {
  border-bottom: 1px solid var(--color-border);
}

/* ===== UTILITY CLASSES ===== */
.text-primary {
  color: var(--color-primary) !important;
}
.text-secondary {
  color: var(--color-secondary) !important;
}
.text-success {
  color: var(--color-success) !important;
}
.text-warning {
  color: var(--color-warning) !important;
}
.text-danger {
  color: var(--color-danger) !important;
}
.text-muted {
  color: var(--color-text-light) !important;
}

.bg-primary {
  background: var(--gradient-primary) !important;
}
.bg-surface {
  background: var(--gradient-surface) !important;
}

/* ===== FOOTER ===== */
footer {
  margin-top: auto;
  background: #1e293b;
  border-top: 1px solid var(--color-border);
  color: #f8fafc;
  padding: 2rem 0;
}

/* ===== PREMIUM IMAGES ===== */
.img-fluid {
  max-width: 100%;
  height: auto;
  transition: all var(--transition-smooth);
}

.hero-image {
  max-height: 500px;
  object-fit: contain;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
  animation: float-gentle 6s ease-in-out infinite;
}

.illustration-image {
  max-height: 350px;
  object-fit: contain;
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.2));
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
}

.illustration-image:hover {
  transform: scale(1.03) translateY(-8px);
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.25)) brightness(1.05);
}

.avatar-image {
  object-fit: cover;
  transition: all var(--transition-smooth);
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}

/* ===== LOADING STATES ===== */
img {
  transition: all var(--transition-smooth);
  opacity: 1;
}

img:not([src]) {
  opacity: 0;
  transform: scale(0.9);
}

img[src] {
  animation: imageLoad 0.5s var(--bounce);
}

@keyframes imageLoad {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* ===== PREMIUM NOTIFICATIONS ===== */
.toast-container {
  position: fixed;
  top: 30px;
  right: 30px;
  z-index: 9999;
  max-width: 400px;
}

.modern-toast {
  min-width: 380px;
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--elevation-4);
  animation: slideInRight 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
}

.modern-toast::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
}

.modern-toast.success::before {
  background: linear-gradient(135deg, var(--color-success) 0%, #22c55e 100%);
}

.modern-toast.error::before {
  background: linear-gradient(135deg, var(--color-danger) 0%, #dc2626 100%);
}

.modern-toast.warning::before {
  background: linear-gradient(135deg, var(--color-warning) 0%, #f59e0b 100%);
}

.modern-toast.info::before {
  background: var(--gradient-primary);
}

.toast-header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-weight: 700;
  font-family: var(--font-display);
  padding: 1rem 1.5rem 0.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.toast-body {
  color: #ffffff;
  font-size: 0.95rem;
  line-height: 1.6;
  padding: 0.5rem 1.5rem 1.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.toast-emoji {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  animation: bounce 2s infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* ===== PREMIUM ANIMATIONS ===== */
@keyframes slideInRight {
  0% {
    transform: translateX(120%) scale(0.8);
    opacity: 0;
  }
  60% {
    transform: translateX(-10%) scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-15px) scale(1.1);
  }
  60% {
    transform: translateY(-8px) scale(1.05);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.pulse-animation {
  animation: pulse 3s infinite;
}

/* ===== PREMIUM PROGRESS BARS ===== */
.progress-modern {
  height: 8px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
}

.progress-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
}

.progress-modern .progress-bar {
  background: var(--success-gradient);
  border-radius: 10px;
  transition: width 0.6s var(--bounce);
  position: relative;
  overflow: hidden;
}

.progress-modern .progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ===== FOOTER IMPROVEMENTS ===== */
footer .text-white-50 {
  color: #cbd5e1 !important;
  text-shadow: none;
}

footer .text-white-50:hover {
  color: #ffffff !important;
  text-shadow: none;
}

footer h5,
footer h6 {
  color: #ffffff;
  text-shadow: none;
}

footer p {
  color: #cbd5e1;
  text-shadow: none;
}

/* ===== PREMIUM ALERTS ===== */
.alert {
  background: #ffffff;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  color: var(--color-text);
  border-left: 4px solid;
  position: relative;
  overflow: hidden;
  text-shadow: none;
  box-shadow: var(--elevation-1);
}

.alert::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
}

.alert-success {
  border-left-color: var(--color-success);
}

.alert-danger {
  border-left-color: var(--color-danger);
}

.alert-warning {
  border-left-color: var(--color-warning);
}

.alert-info {
  border-left-color: var(--color-secondary);
}

/* ===== PREMIUM BADGES ===== */
.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, var(--color-danger) 0%, #dc2626 100%);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
  box-shadow: var(--elevation-2);
}

/* ===== PREMIUM LOADING STATES ===== */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.loading-dots::after {
  content: "";
  display: inline-flex;
  gap: 0.25rem;
  animation: dots 1.8s infinite;
}

@keyframes dots {
  0%,
  20% {
    content: "●";
    opacity: 0.4;
  }
  40% {
    content: "● ●";
    opacity: 0.7;
  }
  60% {
    content: "● ● ●";
    opacity: 1;
  }
  80%,
  100% {
    content: "● ● ● ●";
    opacity: 0.4;
  }
}

/* ===== PREMIUM CONFETTI ===== */
.confetti {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10000;
}

.confetti-piece {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--gradient-primary);
  border-radius: 50%;
  animation: confetti-fall 4s linear infinite;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg) scale(0);
    opacity: 0;
  }
}

/* ===== PREMIUM RIPPLE EFFECTS ===== */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.8s ease-out;
  pointer-events: none;
}

@keyframes ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== PREMIUM TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-display);
  font-weight: 700;
  color: var(--color-text);
  text-shadow: none;
}

.display-1,
.display-2,
.display-3,
.display-4 {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* ===== PREMIUM LIST STYLES ===== */
.list-group-item {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  transition: all var(--transition-base);
  margin-bottom: 0.5rem;
  border-radius: var(--border-radius-md);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.list-group-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(10px);
  border-color: rgba(255, 255, 255, 0.3);
}

/* ===== PREMIUM TYPING INDICATOR ===== */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.typing-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--gradient-primary);
  animation: typing 1.6s infinite ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}
.typing-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ===== PREMIUM SUCCESS ANIMATIONS ===== */
.checkmark {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #4facfe;
  stroke-miterlimit: 10;
  margin: 2rem auto;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--elevation-3);
  animation: checkmark-appear 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)
    forwards;
}

@keyframes checkmark-appear {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* ===== PREMIUM RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .hero-image {
    max-height: 400px;
  }

  .illustration-image {
    max-height: 300px;
  }
}

@media (max-width: 992px) {
  .navbar-brand {
    font-size: 1.25rem;
  }

  .card {
    margin-bottom: 1.5rem;
  }

  .dashboard-card:hover {
    transform: translateY(-8px) scale(1.02);
  }
}

@media (max-width: 768px) {
  /* Mobile Navigation */
  .navbar {
    padding: 0.75rem 1rem;
  }

  /* Mobile Cards */
  .card {
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
  }

  .card:hover {
    transform: translateY(-5px) scale(1.01);
  }

  /* Mobile Forms */
  .form-control {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }

  /* Mobile OTP */
  .otp-container {
    gap: 0.5rem;
    margin: 1.5rem 0;
  }

  .otp-digit {
    width: 45px;
    height: 55px;
    font-size: 1.5rem;
  }

  /* Mobile Notifications */
  .toast-container {
    top: 20px;
    right: 15px;
    left: 15px;
    max-width: none;
  }

  .modern-toast {
    min-width: auto;
    width: 100%;
  }

  /* Mobile Images */
  .hero-image {
    max-height: 250px;
  }

  .illustration-image {
    max-height: 200px;
  }

  /* Mobile Typography */
  h1 {
    font-size: 2rem;
  }
  h2 {
    font-size: 1.75rem;
  }
  h3 {
    font-size: 1.5rem;
  }

  /* Mobile Dashboard */
  .dashboard-card .card-body {
    padding: 1.5rem;
  }

  /* Mobile Feature Icons */
  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.25rem;
  }
}

@media (max-width: 576px) {
  /* Extra Small Mobile */
  .container-fluid {
    padding: 0 1rem;
  }

  .otp-digit {
    width: 40px;
    height: 50px;
    font-size: 1.25rem;
  }

  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }

  .card-body {
    padding: 1.25rem;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }
}

/* ===== PREMIUM ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  body::before {
    animation: none;
  }

  .hero-image {
    animation: none;
  }
}

/* ===== PREMIUM FOCUS STATES ===== */
.btn:focus,
.form-control:focus,
.otp-digit:focus {
  outline: 3px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* ===== PREMIUM PRINT STYLES ===== */
@media print {
  body::before,
  .confetti,
  .toast-container {
    display: none !important;
  }

  .card {
    border: 1px solid #000 !important;
    background: white !important;
    color: black !important;
  }
}
